import './assets/main.css'

import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';

import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { CssBaseline } from '@mui/material'
import App from './App'
import { ThemeWrapper } from './components/ThemeWrapper'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeWrapper>
      <CssBaseline />
      <App />
    </ThemeWrapper>
  </StrictMode>
)
