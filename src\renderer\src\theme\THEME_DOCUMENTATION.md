# Material UI Theme System Documentation

This document provides comprehensive instructions on how to use, customize, and extend the Material UI theme system implemented in this Next.js Electron application.

## Overview

The theme system is built with Material UI v7 and provides:
- ✅ Comprehensive color palette with light/dark mode support
- ✅ Typography system with responsive font sizes
- ✅ Component style overrides for consistent design
- ✅ Spacing and breakpoint configurations
- ✅ Custom theme properties and utilities
- ✅ TypeScript support with proper type definitions
- ✅ React hooks for theme management

## Project Structure

```
src/renderer/src/theme/
├── index.ts              # Main theme exports
├── types.ts              # TypeScript definitions
├── palette.ts            # Color palette configuration
├── typography.ts         # Typography settings
├── spacing.ts            # Spacing system
├── breakpoints.ts        # Responsive breakpoints
├── components.ts         # Component style overrides
├── utils.ts              # Theme utility functions
├── hooks.ts              # React hooks for theme management
├── theme.ts              # Main theme configuration
├── lightTheme.ts         # Light theme variant
└── darkTheme.ts          # Dark theme variant
```

## Getting Started

### Basic Usage

The theme is automatically applied to your application through the `ThemeWrapper` component in `main.tsx`:

```tsx
import { ThemeWrapper } from './components/ThemeWrapper'

// Your app is wrapped with ThemeWrapper
<ThemeWrapper>
  <CssBaseline />
  <App />
</ThemeWrapper>
```

### Using Theme in Components

```tsx
import { useTheme } from '@mui/material/styles'
import { useThemeContext } from './components/ThemeWrapper'

function MyComponent() {
  const theme = useTheme()
  const { mode, toggleMode, isDark } = useThemeContext()
  
  return (
    <Box sx={{ 
      backgroundColor: theme.palette.primary.main,
      padding: theme.spacing(2),
      borderRadius: theme.custom.borderRadius.medium 
    }}>
      <Button onClick={toggleMode}>
        Switch to {isDark ? 'light' : 'dark'} mode
      </Button>
    </Box>
  )
}
```

## Theme Customization

### 1. Color Palette

#### Using Built-in Colors

The theme includes comprehensive color palettes:

```tsx
// Primary colors
theme.palette.primary.main      // #2196f3
theme.palette.primary.light     // #64b5f6
theme.palette.primary.dark      // #1976d2
theme.palette.primary.lighter   // #e3f2fd
theme.palette.primary.darker    // #0d47a1

// Custom colors
theme.palette.tertiary.main     // #009688 (teal)
theme.palette.accent.main       // #ff9800 (orange)
```

#### Creating Custom Colors

```tsx
import { createCustomTheme } from './theme'

const customTheme = createCustomTheme({
  mode: 'light',
  primaryColor: '#ff5722',      // Custom primary color
  secondaryColor: '#4caf50',    // Custom secondary color
})
```

### 2. Typography

#### Using Typography Variants

```tsx
<Typography variant="h1">Main Heading</Typography>
<Typography variant="body1">Body text</Typography>
<Typography variant="caption">Small text</Typography>
```

#### Custom Typography

```tsx
// Responsive font sizes are built-in
<Typography 
  variant="h3" 
  sx={{ 
    fontSize: { xs: '1.5rem', md: '2rem', lg: '2.5rem' }
  }}
>
  Responsive Heading
</Typography>
```

### 3. Spacing

#### Using the Spacing System

```tsx
// Built-in spacing values
theme.spacing(1)    // 8px
theme.spacing(2)    // 16px
theme.spacing(3)    // 24px

// Predefined spacing values
import { spacingValues } from './theme'

<Box sx={{ 
  padding: spacingValues.md,     // 16px
  margin: spacingValues.lg,      // 24px
}}>
```

#### Responsive Spacing

```tsx
import { useResponsive } from './theme/hooks'

function MyComponent() {
  const { getResponsiveSpacing } = useResponsive()
  
  return (
    <Box sx={getResponsiveSpacing(2, 4)}>
      {/* 16px padding on mobile, 32px on desktop */}
    </Box>
  )
}
```

### 4. Component Customization

#### Using Styled Components

```tsx
import { styled } from '@mui/material/styles'

const CustomButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.custom.borderRadius.large,
  padding: theme.spacing(1.5, 3),
  boxShadow: theme.custom.shadows.button,
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[4],
  },
}))
```

#### Using sx Prop

```tsx
<Button
  sx={{
    borderRadius: 'custom.borderRadius.medium',
    bgcolor: 'primary.main',
    '&:hover': {
      bgcolor: 'primary.dark',
      transform: 'scale(1.05)',
    },
  }}
>
  Custom Button
</Button>
```

## Advanced Usage

### 1. Custom Hooks

#### Theme Mode Management

```tsx
import { useThemeMode } from './theme/hooks'

function ThemeToggle() {
  const { mode, toggleMode, isDark } = useThemeMode()
  
  return (
    <IconButton onClick={toggleMode}>
      {isDark ? <Brightness7 /> : <Brightness4 />}
    </IconButton>
  )
}
```

#### Responsive Design

```tsx
import { useResponsive } from './theme/hooks'

function ResponsiveComponent() {
  const { isMobile, isDesktop, getResponsiveValue } = useResponsive()
  
  const columns = getResponsiveValue(1, 2, 3, 4) // mobile, tablet, desktop, large
  
  return (
    <Grid container spacing={2}>
      {/* Responsive grid */}
    </Grid>
  )
}
```

#### Theme Colors

```tsx
import { useThemeColors } from './theme/hooks'

function ColorShowcase() {
  const { colors, gradients, isDark } = useThemeColors()
  
  return (
    <Box sx={{ 
      background: gradients.primary,
      color: colors.primary.contrastText 
    }}>
      Gradient Background
    </Box>
  )
}
```

### 2. Custom Theme Properties

Access custom theme properties:

```tsx
// Custom gradients
theme.custom.gradients.primary
theme.custom.gradients.secondary
theme.custom.gradients.accent

// Custom shadows
theme.custom.shadows.card
theme.custom.shadows.button
theme.custom.shadows.modal

// Custom border radius
theme.custom.borderRadius.small    // 4px
theme.custom.borderRadius.medium   // 8px
theme.custom.borderRadius.large    // 16px
```

### 3. Utility Functions

```tsx
import { colorUtils, animationUtils } from './theme/utils'

// Color manipulation
const transparentPrimary = colorUtils.withAlpha('#2196f3', 0.5)
const darkerPrimary = colorUtils.darken('#2196f3', 0.2)

// Animations
const fadeTransition = animationUtils.createTransition(['opacity'], 300)
```

## Best Practices

### 1. Consistent Spacing

Always use the theme spacing system:

```tsx
// ✅ Good
<Box sx={{ padding: theme.spacing(2) }}>

// ❌ Avoid
<Box sx={{ padding: '16px' }}>
```

### 2. Responsive Design

Use theme breakpoints for consistency:

```tsx
// ✅ Good
<Box sx={{ 
  fontSize: { xs: '1rem', md: '1.25rem' },
  padding: { xs: 2, md: 3 }
}}>

// ❌ Avoid
<Box sx={{ 
  fontSize: '1rem',
  '@media (min-width: 960px)': { fontSize: '1.25rem' }
}}>
```

### 3. Color Usage

Use semantic color names:

```tsx
// ✅ Good
<Alert severity="error">Error message</Alert>
<Button color="primary">Primary Action</Button>

// ❌ Avoid
<Alert sx={{ backgroundColor: '#f44336' }}>Error message</Alert>
```

### 4. Component Styling

Prefer theme-based styling:

```tsx
// ✅ Good
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.custom.borderRadius.large,
  boxShadow: theme.custom.shadows.card,
}))

// ❌ Avoid
const StyledCard = styled(Card)({
  borderRadius: '16px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
})
```

## Extending the Theme

### Adding Custom Colors

1. Update `types.ts` to add TypeScript definitions:

```tsx
declare module '@mui/material/styles' {
  interface Palette {
    custom: Palette['primary'];
  }
  interface PaletteOptions {
    custom?: PaletteOptions['primary'];
  }
}
```

2. Add the color to your palette configuration in `palette.ts`.

3. Update component prop overrides if needed.

### Adding Custom Components

Add component overrides in `components.ts`:

```tsx
export const components: Components<Theme> = {
  // ... existing components
  MuiCustomComponent: {
    styleOverrides: {
      root: ({ theme }) => ({
        // Your custom styles
      }),
    },
  },
}
```

## Troubleshooting

### Common Issues

1. **Theme not applied**: Ensure `ThemeWrapper` wraps your app
2. **TypeScript errors**: Check module augmentation in `types.ts`
3. **Colors not working**: Verify color names match palette definitions
4. **Responsive issues**: Use theme breakpoints instead of custom media queries

### Performance Tips

1. Use `useMemo` for expensive theme calculations
2. Avoid inline styles when possible
3. Use theme caching for custom themes
4. Minimize theme provider nesting

## Migration Guide

If migrating from an existing theme system:

1. Replace custom CSS with theme-based styling
2. Update color references to use palette
3. Convert spacing to theme.spacing()
4. Replace media queries with theme breakpoints
5. Update component styling to use theme overrides

This theme system provides a solid foundation for building consistent, accessible, and maintainable user interfaces in your Electron application.
