/**
 * Breakpoint configuration for responsive design
 * Following Material Design responsive layout guidelines
 */

import { BreakpointsOptions } from '@mui/material/styles';

// Breakpoint values in pixels
export const breakpointValues = {
  xs: 0,      // Extra small devices (phones)
  sm: 600,    // Small devices (tablets)
  md: 960,    // Medium devices (small laptops)
  lg: 1280,   // Large devices (desktops)
  xl: 1920,   // Extra large devices (large desktops)
} as const;

// Breakpoints configuration for Material UI theme
export const breakpoints: BreakpointsOptions = {
  values: breakpointValues,
  unit: 'px',
  step: 5, // Increment used to implement exclusive breakpoints
};

// Custom breakpoint utilities
export const customBreakpoints = {
  // Mobile-first breakpoints
  mobile: breakpointValues.xs,
  tablet: breakpointValues.sm,
  laptop: breakpointValues.md,
  desktop: breakpointValues.lg,
  widescreen: breakpointValues.xl,
  
  // Specific device breakpoints
  mobilePortrait: 320,
  mobileLandscape: 568,
  tabletPortrait: 768,
  tabletLandscape: 1024,
  
  // Content-based breakpoints
  maxContentWidth: 1200, // Maximum content width for readability
  sidebarBreakpoint: 960, // When to show/hide sidebar
  navigationBreakpoint: 768, // When to switch to mobile navigation
} as const;

// Responsive design utilities
export const responsiveUtils = {
  // Check if screen size is mobile
  isMobile: `@media (max-width: ${breakpointValues.sm - 1}px)`,
  
  // Check if screen size is tablet
  isTablet: `@media (min-width: ${breakpointValues.sm}px) and (max-width: ${breakpointValues.md - 1}px)`,
  
  // Check if screen size is desktop
  isDesktop: `@media (min-width: ${breakpointValues.md}px)`,
  
  // Check if screen size is large desktop
  isLargeDesktop: `@media (min-width: ${breakpointValues.lg}px)`,
  
  // Mobile-first media queries
  up: {
    sm: `@media (min-width: ${breakpointValues.sm}px)`,
    md: `@media (min-width: ${breakpointValues.md}px)`,
    lg: `@media (min-width: ${breakpointValues.lg}px)`,
    xl: `@media (min-width: ${breakpointValues.xl}px)`,
  },
  
  // Desktop-first media queries
  down: {
    xs: `@media (max-width: ${breakpointValues.sm - 1}px)`,
    sm: `@media (max-width: ${breakpointValues.md - 1}px)`,
    md: `@media (max-width: ${breakpointValues.lg - 1}px)`,
    lg: `@media (max-width: ${breakpointValues.xl - 1}px)`,
  },
  
  // Between breakpoints
  between: {
    smMd: `@media (min-width: ${breakpointValues.sm}px) and (max-width: ${breakpointValues.md - 1}px)`,
    mdLg: `@media (min-width: ${breakpointValues.md}px) and (max-width: ${breakpointValues.lg - 1}px)`,
    lgXl: `@media (min-width: ${breakpointValues.lg}px) and (max-width: ${breakpointValues.xl - 1}px)`,
  },
  
  // Only specific breakpoints
  only: {
    xs: `@media (max-width: ${breakpointValues.sm - 1}px)`,
    sm: `@media (min-width: ${breakpointValues.sm}px) and (max-width: ${breakpointValues.md - 1}px)`,
    md: `@media (min-width: ${breakpointValues.md}px) and (max-width: ${breakpointValues.lg - 1}px)`,
    lg: `@media (min-width: ${breakpointValues.lg}px) and (max-width: ${breakpointValues.xl - 1}px)`,
    xl: `@media (min-width: ${breakpointValues.xl}px)`,
  },
};

// Container max-widths for different breakpoints
export const containerMaxWidths = {
  xs: false,
  sm: 540,
  md: 720,
  lg: 960,
  xl: 1140,
} as const;

// Grid system configuration
export const gridConfig = {
  columns: 12, // Number of columns in the grid
  spacing: 2,  // Default spacing between grid items (in spacing units)
  
  // Responsive spacing for grid
  responsiveSpacing: {
    xs: 1, // 8px
    sm: 2, // 16px
    md: 3, // 24px
    lg: 3, // 24px
    xl: 3, // 24px
  },
} as const;
